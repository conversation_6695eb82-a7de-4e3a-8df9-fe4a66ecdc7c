"""
Agent服务 - 处理AI Agent相关的业务逻辑
"""
import asyncio
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

import structlog
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from ..agent.graph import build_agent_graph
from ..core.models import Agent<PERSON>he<PERSON><PERSON>, PendingAction, User
from ..core.schemas import (
    AgentState,
    ProcessSignalRequest,
    TaskCreatedResponse,
)

logger = structlog.get_logger()


class AgentService:
    """Agent服务类"""

    def __init__(self, session_factory=None):
        self.agent_graph = None
        self._running_tasks: Dict[str, asyncio.Task] = {}
        self._session_factory = session_factory

    async def get_agent_graph(self, db: AsyncSession):
        """获取Agent图实例"""
        if self.agent_graph is None:
            self.agent_graph = build_agent_graph(db)
        return self.agent_graph

    async def process_signal(
        self, request: ProcessSignalRequest, user: User, db: AsyncSession
    ) -> TaskCreatedResponse:
        """
        处理交易信号

        Args:
            request: 处理请求
            user: 用户对象
            db: 数据库会话

        Returns:
            TaskCreatedResponse: 任务创建响应
        """
        task_id = str(uuid.uuid4())

        # 创建初始状态
        import uuid as uuid_module

        # 从context中提取signal_id
        signal_id = None
        if request.context and 'signal_id' in request.context:
            try:
                signal_id = uuid_module.UUID(request.context['signal_id'])
            except (ValueError, TypeError):
                signal_id = None

        # 如果没有signal_id，记录警告日志
        if signal_id is None:
            logger.warning(
                "Agent处理请求缺少signal_id - 可能绕过了SignalService",
                task_id=task_id,
                user_id=user.id,
                raw_input=request.raw_input[:100],
                context_keys=list(request.context.keys()) if request.context else []
            )

        # 预先获取用户风控配置以支持路由决策
        context = request.context or {}
        try:
            from ..core.models import RiskConfig
            from sqlalchemy import select

            query = select(RiskConfig).where(RiskConfig.user_id == user.id)
            result = await db.execute(query)
            risk_config = result.scalar_one_or_none()

            if risk_config:
                context["risk_config"] = {
                    "confidence_threshold": float(risk_config.confidence_threshold),
                    "auto_approve_threshold": float(risk_config.auto_approve_threshold),
                    "max_concurrent_orders": risk_config.max_concurrent_orders,
                    "max_total_position_value_usd": float(risk_config.max_total_position_value_usd),
                    "default_position_size_usd": float(risk_config.default_position_size_usd),
                    "max_position_size_usd": float(risk_config.max_position_size_usd),
                    "allowed_symbols": risk_config.allowed_symbols,
                }
                logger.info(
                    "已加载用户风控配置",
                    user_id=user.id,
                    confidence_threshold=float(risk_config.confidence_threshold),
                    auto_approve_threshold=float(risk_config.auto_approve_threshold)
                )
            else:
                # 使用默认配置
                context["risk_config"] = {
                    "confidence_threshold": 0.8,
                    "auto_approve_threshold": 0.95,
                    "max_concurrent_orders": 5,
                    "max_total_position_value_usd": 1000.0,
                    "default_position_size_usd": 100.0,
                    "max_position_size_usd": 500.0,
                    "allowed_symbols": ["BTC/USDT", "ETH/USDT"],
                }
                logger.info("使用默认风控配置", user_id=user.id)

        except Exception as e:
            logger.warning("获取风控配置失败，使用默认配置", user_id=user.id, error=str(e))
            context["risk_config"] = {
                "confidence_threshold": 0.8,
                "auto_approve_threshold": 0.95,
                "max_concurrent_orders": 5,
                "max_total_position_value_usd": 1000.0,
                "default_position_size_usd": 100.0,
                "max_position_size_usd": 500.0,
                "allowed_symbols": ["BTC/USDT", "ETH/USDT"],
            }

        initial_state = AgentState(
            task_id=uuid_module.UUID(task_id),  # 明确设置task_id，避免自动生成
            user_id=user.id,
            signal_id=signal_id,  # 设置signal_id
            raw_input=request.raw_input,
            context=context,
        )

        try:
            # 获取Agent图
            graph = await self.get_agent_graph(db)

            # 启动异步任务处理
            task = asyncio.create_task(
                self._process_agent_task(task_id, initial_state, db)
            )
            self._running_tasks[task_id] = task

            logger.info(
                "Agent任务已创建",
                task_id=task_id,
                user_id=user.id,
                raw_input=request.raw_input,
            )

            return {
                "task_id": task_id,
                "status": "processing",
                "message": "任务已创建，正在处理中"
            }

        except Exception as e:
            logger.error("创建Agent任务失败", task_id=task_id, user_id=user.id, error=str(e))
            raise

    async def _process_agent_task(
        self, task_id: str, initial_state: AgentState, db: AsyncSession
    ):
        """
        处理Agent任务的内部方法

        Args:
            task_id: 任务ID
            initial_state: 初始状态
            db: 数据库会话
        """
        # 为后台任务创建新的数据库会话
        if self._session_factory:
            async_session = self._session_factory()
        else:
            from ..core.database import AsyncSessionLocal

            async_session = AsyncSessionLocal()

        try:
            # 更新信号状态为处理中
            await self._update_signal_status_on_start(task_id, initial_state, async_session)

            # 获取Agent图
            graph = await self.get_agent_graph(async_session)

            # 执行Agent工作流
            from ..agent.graph import run_agent

            result = await run_agent(initial_state, async_session)

            # 提取AgentState对象
            if isinstance(result, dict) and "state" in result:
                final_state = result["state"]
            else:
                final_state = result

            # 保存最终结果
            await self._save_final_result(task_id, final_state, async_session)

            # 更新关联信号的状态
            await self._update_signal_status_on_completion(task_id, final_state, async_session)

            logger.info(
                "Agent任务完成",
                task_id=task_id,
                user_id=initial_state.user_id,
                status="success",
            )

        except Exception as e:
            logger.error(
                "Agent任务执行失败",
                task_id=task_id,
                user_id=initial_state.user_id,
                error=str(e),
            )

            # 保存错误结果
            await self._save_error_result(task_id, str(e), initial_state.user_id, async_session)

            # 更新关联信号的状态为失败
            await self._update_signal_status_on_error(task_id, str(e), async_session)

        finally:
            # 关闭数据库会话
            await async_session.close()
            # 清理任务
            if task_id in self._running_tasks:
                del self._running_tasks[task_id]

    async def _save_final_result(
        self, task_id: str, result: AgentState, db: AsyncSession
    ):
        """保存最终结果"""
        try:
            # 使用检查点管理器的序列化功能
            from ..agent.checkpoint import CheckpointManager

            checkpoint_manager = CheckpointManager(db)

            # 保存最终检查点
            await checkpoint_manager.save_checkpoint(result, "Final")

        except Exception as e:
            logger.error("保存Agent结果失败", task_id=task_id, error=str(e))
            await db.rollback()

    async def _save_error_result(
        self, task_id: str, error_message: str, user_id: uuid.UUID, db: AsyncSession
    ):
        """保存错误结果"""
        try:
            # 创建错误检查点
            checkpoint = AgentCheckpoint(
                task_id=task_id,
                user_id=user_id,
                node_name="Error",
                state_data={
                    "error": error_message,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                },
            )

            db.add(checkpoint)
            await db.commit()

        except Exception as e:
            logger.error("保存Agent错误失败", task_id=task_id, error=str(e))
            await db.rollback()

    async def _update_signal_status_on_start(
        self, task_id: str, initial_state: AgentState, db: AsyncSession
    ):
        """Agent任务开始时更新关联信号的状态"""
        try:
            # 从状态中获取signal_id
            signal_id = initial_state.signal_id
            if not signal_id:
                logger.warning("Agent任务开始但未找到关联的signal_id", task_id=task_id)
                return

            # 查找信号并更新状态
            from ..core.models import Signal
            from sqlalchemy import update

            # 更新信号状态为处理中，并设置agent_task_id
            try:
                task_uuid = uuid.UUID(task_id)
            except ValueError:
                logger.error("无效的task_id格式", task_id=task_id)
                return

            update_stmt = (
                update(Signal)
                .where(Signal.id == signal_id)
                .values(
                    agent_task_id=task_uuid,
                    agent_processing_status="processing"
                    # updated_at会由模型的onupdate=utc_now自动更新
                )
            )
            await db.execute(update_stmt)
            await db.commit()

            logger.info(
                "信号状态已更新为处理中",
                task_id=task_id,
                signal_id=str(signal_id),
                status="processing"
            )

        except Exception as e:
            logger.error(
                "更新信号开始状态失败",
                task_id=task_id,
                error=str(e),
                exc_info=True
            )

    async def _update_signal_status_on_completion(
        self, task_id: str, final_state: "AgentState", db: AsyncSession
    ):
        """Agent任务完成后更新关联信号的状态"""
        try:
            logger.info("调试：_update_signal_status_on_completion方法被调用", task_id=task_id)
            # 调试日志：检查final_state的内容
            logger.info(
                "调试：检查final_state内容",
                task_id=task_id,
                final_state_type=type(final_state).__name__,
                has_signal_id=hasattr(final_state, 'signal_id'),
                signal_id_value=getattr(final_state, 'signal_id', None),
                context_signal_id=final_state.context.get("signal_id") if hasattr(final_state, 'context') else None
            )

            # 从状态中获取signal_id - 优先从signal_id字段获取，然后从context获取
            signal_id = final_state.signal_id
            if not signal_id:
                signal_id = final_state.context.get("signal_id")

            if not signal_id:
                logger.warning("Agent任务完成但未找到关联的signal_id", task_id=task_id)
                return

            # 导入Signal模型
            from ..core.models import Signal

            # 转换signal_id为UUID
            try:
                if isinstance(signal_id, uuid.UUID):
                    signal_uuid = signal_id
                else:
                    signal_uuid = uuid.UUID(signal_id)
            except (ValueError, TypeError):
                logger.error("无效的signal_id格式", task_id=task_id, signal_id=signal_id)
                return

            # 确定最终状态
            final_status = "completed"
            if hasattr(final_state, 'error_message') and final_state.error_message:
                final_status = "failed"
            elif hasattr(final_state, 'final_result'):
                if isinstance(final_state.final_result, dict):
                    if final_state.final_result.get("success") is False:
                        final_status = "failed"

            # 更新信号状态
            update_stmt = (
                update(Signal)
                .where(Signal.id == signal_uuid)
                .values(
                    agent_processing_status=final_status
                    # updated_at会由模型的onupdate=utc_now自动更新
                )
            )

            await db.execute(update_stmt)
            await db.commit()

            logger.info(
                "信号状态已更新",
                task_id=task_id,
                signal_id=signal_id,
                status=final_status
            )

        except Exception as e:
            logger.error(
                "更新信号状态失败",
                task_id=task_id,
                error=str(e),
                exc_info=True
            )
            await db.rollback()

    async def _update_signal_status_on_error(
        self, task_id: str, error_message: str, db: AsyncSession
    ):
        """Agent任务失败后更新关联信号的状态"""
        try:
            # 查找与此任务关联的信号
            from ..core.models import Signal

            # 通过agent_task_id查找信号
            try:
                task_uuid = uuid.UUID(task_id)
            except ValueError:
                logger.error("无效的task_id格式", task_id=task_id)
                return

            # 查找信号
            signal_stmt = select(Signal).where(Signal.agent_task_id == task_uuid)
            result = await db.execute(signal_stmt)
            signal = result.scalar_one_or_none()

            if not signal:
                logger.warning("未找到与任务关联的信号", task_id=task_id)
                return

            # 更新信号状态为失败
            update_stmt = (
                update(Signal)
                .where(Signal.id == signal.id)
                .values(
                    agent_processing_status="failed"
                    # updated_at会由模型的onupdate=utc_now自动更新
                )
            )

            await db.execute(update_stmt)
            await db.commit()

            logger.info(
                "信号状态已更新为失败",
                task_id=task_id,
                signal_id=str(signal.id),
                error=error_message
            )

        except Exception as e:
            logger.error(
                "更新信号失败状态时出错",
                task_id=task_id,
                error=str(e),
                exc_info=True
            )
            await db.rollback()

    async def get_task_status(
        self, task_id: str, db: AsyncSession
    ) -> Optional[Dict[str, Any]]:
        """
        获取任务状态

        Args:
            task_id: 任务ID
            db: 数据库会话

        Returns:
            Optional[Dict[str, Any]]: 任务状态信息
        """
        try:
            # 将字符串task_id转换为UUID
            import uuid as uuid_module

            try:
                task_uuid = uuid_module.UUID(task_id)
            except ValueError:
                logger.warning("Invalid task_id format", task_id=task_id)
                return None

            # 查询最新的检查点
            stmt = (
                select(AgentCheckpoint)
                .where(AgentCheckpoint.task_id == task_uuid)
                .order_by(AgentCheckpoint.created_at.desc())
                .limit(1)
            )

            result = await db.execute(stmt)
            checkpoint = result.scalar_one_or_none()

            if not checkpoint:
                # 如果没有找到检查点，检查任务是否在运行中
                is_running = task_id in self._running_tasks
                if is_running:
                    return {
                        "task_id": task_id,
                        "status": "running",
                        "current_node": "Processing",
                        "last_update": None,
                        "state_data": {},
                    }
                return None

            # 检查任务是否还在运行
            is_running = task_id in self._running_tasks

            # 确定任务状态
            if is_running:
                status = "running"
            else:
                # 检查是否失败 - 首先检查状态数据中的status字段
                if checkpoint.state_data and isinstance(checkpoint.state_data, dict):
                    # 检查状态数据中的status字段
                    state_status = checkpoint.state_data.get("status")
                    if state_status == "failed":
                        status = "failed"
                    elif checkpoint.node_name in [
                        "Failure",
                        "Failed",
                        "Error",
                    ]:
                        status = "failed"
                    else:
                        # 检查其他失败标识
                        final_result = checkpoint.state_data.get("final_result")
                        if final_result and isinstance(final_result, dict):
                            if final_result.get("success") is False:
                                status = "failed"
                            else:
                                status = "completed"
                        else:
                            status = "completed"
                elif checkpoint.node_name in ["Failure", "Failed", "Error"]:
                    status = "failed"
                else:
                    status = "completed"

            return {
                "task_id": task_id,
                "status": status,
                "current_node": checkpoint.node_name,
                "last_update": checkpoint.created_at.isoformat(),
                "state_data": checkpoint.state_data,
            }

        except Exception as e:
            logger.error("获取任务状态失败", task_id=task_id, error=str(e))
            return None

    async def list_user_tasks(
        self, user_id: int, db: AsyncSession, limit: int = 20, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        列出用户的任务

        Args:
            user_id: 用户ID
            db: 数据库会话
            limit: 限制数量
            offset: 偏移量

        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        try:
            # 查询用户的检查点，按任务分组
            stmt = (
                select(AgentCheckpoint)
                .where(AgentCheckpoint.user_id == user_id)
                .order_by(AgentCheckpoint.created_at.desc())
                .limit(limit)
                .offset(offset)
            )

            result = await db.execute(stmt)
            checkpoints = result.scalars().all()

            # 按任务ID分组
            tasks = {}
            for checkpoint in checkpoints:
                task_id = checkpoint.task_id
                if task_id not in tasks:
                    tasks[task_id] = {
                        "task_id": task_id,
                        "status": "running"
                        if task_id in self._running_tasks
                        else "completed",
                        "created_at": checkpoint.created_at.isoformat(),
                        "last_node": checkpoint.node_name,
                        "state_data": checkpoint.state_data,
                    }

            return list(tasks.values())

        except Exception as e:
            logger.error("列出用户任务失败", user_id=user_id, error=str(e))
            return []

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功取消
        """
        if task_id in self._running_tasks:
            task = self._running_tasks[task_id]
            task.cancel()
            del self._running_tasks[task_id]

            logger.info("任务已取消", task_id=task_id)
            return True

        return False

    def get_running_tasks_count(self) -> int:
        """获取正在运行的任务数量"""
        return len(self._running_tasks)


# 全局Agent服务实例
agent_service = AgentService()
